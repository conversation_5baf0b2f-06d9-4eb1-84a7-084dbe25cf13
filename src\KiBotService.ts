export type AskKiBotProps = { conversationId: string; question: string; productId: string };
export type AskKiBotOnSuccessParams = {
  answer: string;
  conversationId: string;
  messageId: string;
  status: string;
};
export type OnErrorParams = { status: string; message: string };
export type RateKiBotProps = {
  productId: string;
  comment: string;
  conversationId: string;
  messageId: string;
  rating: number;
};
export type RateKiBotOnSuccessParams = { message: string; status: string };
export type KiBotInfoOnSuccessParams = { build_timestamp: string; service: string; version: string };

export class KiBotService {
  static async askKiBot(props: AskKiBotProps): Promise<AskKiBotOnSuccessParams> {
    const host = window.location.href;
    const response = await fetch(`${host}api/ask`, {
      body: JSON.stringify({
        metadata: {
          productId: props.productId,
          target: props.productId,
          extend: "short",
          language: "de",
        },
        conversationId: props.conversationId,
        question: props.question,
      }),
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorMsg = (await response.json()) as OnErrorParams;
      throw new Error(JSON.stringify(errorMsg));
    }

    return response.json();
  }

  static async rateKiBot(props: RateKiBotProps): Promise<RateKiBotOnSuccessParams> {
    const host = window.location.href;
    const response = await fetch(host + "api/rate", {
      body: JSON.stringify({
        metadata: {
          language: "de",
          productId: props.productId,
          target: props.productId,
        },
        conversationId: props.conversationId,
        comment: props.comment,
        messageId: props.messageId,
        rating: props.rating,
      }),
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    });
    if (!response.ok) {
      const errorMsg = (await response.json()) as OnErrorParams;
      throw new Error(JSON.stringify(errorMsg));
    }
    return response.json();
  }

  static async getKiBotInfo(): Promise<KiBotInfoOnSuccessParams> {
    const host = window.location.href;
    const response = await fetch(host + "api/info");
    if (!response.ok) {
      const errorMsg = (await response.json()) as OnErrorParams;
      throw new Error(JSON.stringify(errorMsg));
    }
    return response.json();
  }
}
