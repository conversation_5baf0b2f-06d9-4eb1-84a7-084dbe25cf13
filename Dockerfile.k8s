ARG REPOSITORY_MIRROR=harbor.rancher.dev-ct.dbh.software

FROM $REPOSITORY_MIRROR/docker/node:22-alpine AS build-stage
ARG BUILD_ID
ARG COMMITID
ARG BUILDDATE="Jan. 12, 1992"
ARG HTTPS_PROXY="http://proxy.prod.local:3128"
ARG HTTP_PROXY="http://proxy.prod.local:3128"
ARG NO_PROXY="*********/8,10.0.0.0/8,**********/12,***********/16,localhost,.local,.internal,.svc,.cluster.local,.dbh.local,.dbh.software"

LABEL stage=build-stage
LABEL build=${COMMITID}

# Setzen des Workspaces und Kopieren der Dateien
WORKDIR /app
COPY . .

RUN echo VITE_NAME=ExampleChatbotName >.env.production &&\
    echo VITE_VERSION=${COMMITID} >>.env.production &&\
    echo VITE_VERSION_BUILD_TIMESTAMP=${BUILDDATE} >>.env.production


# Initialisierung von NPM
RUN npm config --global set loglevel verbose &&\
    npm config --global set strict-ssl false &&\
    npm config --global set rebuild-bundle false &&\
    npm config --global set registry https://artifactory.dbh.local/artifactory/api/npm/npm-virtual-repos


# Dependency Installation und Bauen von Deployment
# Install dependencies first, as they change less often than code.
COPY package.json package-lock.json* ./
RUN npm install && npm ci
RUN npm run production


# Stage 2: Serve Vue app with Nginx
FROM $REPOSITORY_MIRROR/docker/nginx:alpine3.20-slim as prod-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html

#RUN chown node:node ./
#USER node

EXPOSE 8080

CMD ["nginx", "-g", "daemon off;"]


