import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import App from "./App.tsx";
import "dbh-reactui-tokens/css/normalize/normalize.css";
import "dbh-reactui-tokens/css/tokens/tokens.css";
import "dbh-reactui-tokens/css/themes/light/light.css";
import "dbh-reactui-tokens/css/themes/dark/dark.css";
import "./index.css";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import ChatMessagesProvider from "./provider/chat-messages-provider.tsx";

const queryClient = new QueryClient();

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <ChatMessagesProvider>
      <QueryClientProvider client={queryClient}>
        <ReactQueryDevtools initialIsOpen={false} />
        <App />
      </QueryClientProvider>
    </ChatMessagesProvider>
  </StrictMode>,
);
