import React, { useState } from "react";
import {
  Button,
  Link,
  ModalDialog,
  ModalDialogAction,
  ModalDialogCancel,
  ModalDialogContent,
  ModalDialogDescription,
  ModalDialogFooter,
  ModalDialogHeader,
  ModalDialogTitle,
  ModalDialogTrigger,
  Textarea,
  toast_error,
  toast_success,
  Typography,
} from "dbh-reactui";
import <PERSON><PERSON>ogo from "../assets/star-line.svg?react";
import StarFill<PERSON>ogo from "../assets/star-fill.svg?react";
import ThumbDownLineLogo from "../assets/thumb-down-line.svg?react";
import ThumbDownFillLogo from "../assets/thumb-down-fill.svg?react";
import RatingDialogPopup from "./RatingDialogPopup.tsx";
import { useMutation } from "@tanstack/react-query";
import { cn } from "../Utils.ts";
import { KiBotService, OnErrorParams, RateKiBotOnSuccessParams, RateKiBotProps } from "../KiBotService.ts";
import { useChatMessageContext } from "../context/chat-messages-context.ts";

type Props = { productId: string; conversationId: string; answerId: string };

const RatingDialog: React.FC<Props> = ({ productId, conversationId, answerId }) => {
  const { savedChatMessages, setSavedChatMessages } = useChatMessageContext();
  const savedProductMessages = savedChatMessages[productId];
  const chatMessage = savedProductMessages.find(
    (msg) => msg.conversationId === conversationId && msg.answerId === answerId,
  );
  const [hoveredStars, setHoveredStars] = useState<number>(chatMessage?.rated ?? 0); // Sterne beim Hover
  const [selectedStars, setSelectedStars] = useState<number>(chatMessage?.rated ?? 0); // Sterne nach einem Klick
  const [comment, setComment] = useState<string>(chatMessage?.comment ?? "");
  const [rated, setRated] = useState<number | undefined>(chatMessage?.rated);

  function handleSuccess(answer: RateKiBotOnSuccessParams) {
    console.log(answer);
    setRated(selectedStars);
    setSavedChatMessages((prev) => {
      const list = prev[productId] || [];
      return {
        ...prev,
        [productId]: list.map((msg) =>
          msg.conversationId === conversationId && msg.answerId === answerId
            ? { ...msg, rated: selectedStars, comment: comment }
            : msg,
        ),
      };
    });
    toast_success("Erfolgreich bewertet.");
  }

  function handleError(error: OnErrorParams) {
    console.error(error.message);
    toast_error("Bewertung fehlgeschlagen!");
  }

  const mutation = useMutation<RateKiBotOnSuccessParams, OnErrorParams, RateKiBotProps>({
    mutationFn: KiBotService.rateKiBot,
    onSuccess: handleSuccess,
    onError: handleError,
  });

  const renderStar = (index: number) => {
    const isFilled = index <= (hoveredStars || selectedStars);
    return (
      <div key={`star-${index}`}>
        {isFilled ? (
          <StarFillLogo
            className="h-20 w-20 cursor-pointer fill-yellow-400"
            onMouseEnter={() => setHoveredStars(index)} // Hover-Effekt
            onMouseLeave={() => setHoveredStars(0)} // Hover entfernen
            onClick={() => {
              if (index === selectedStars) {
                setSelectedStars(0);
              } else {
                setSelectedStars(index);
              }
            }} // Bewertung setzen
          />
        ) : (
          <StarLogo
            className="h-20 w-20 cursor-pointer fill-primary-100/50"
            onMouseEnter={() => setHoveredStars(index)} // Hover-Effekt
            onMouseLeave={() => setHoveredStars(0)} // Hover entfernen
            onClick={() => setSelectedStars(index)} // Bewertung setzen
          />
        )}
      </div>
    );
  };

  const renderRatedStars = () => {
    return (
      <div className="flex">
        {Array.from({ length: selectedStars }, (_, i) => {
          return <StarFillLogo key={i} className="h-4 w-4 fill-yellow-400" />;
        })}
      </div>
    );
  };

  const renderRating = () => {
    return (
      <div className="flex">
        {selectedStars < 0 ? <ThumbDownFillLogo className="h-4 w-4 fill-red-500" /> : renderRatedStars()}
      </div>
    );
  };

  return (
    <ModalDialog>
      <ModalDialogTrigger asChild>
        {rated != undefined ? (
          <div className="flex gap-1 pr-2">
            <Typography variant="caption_2" className="text-primary-100">
              Bewertet:
            </Typography>
            {renderRating()}
          </div>
        ) : (
          <Link className="w-full whitespace-nowrap pl-4 pr-2 text-end text-primary-100">
            Antwort bewerten
          </Link>
        )}
      </ModalDialogTrigger>
      <ModalDialogContent>
        <ModalDialogDescription className="hidden">Bewerten Sie Ihre Antwort</ModalDialogDescription>
        <ModalDialogHeader>
          <ModalDialogTitle className="text-center text-primary-100">
            Bewerten Sie die Antwort
          </ModalDialogTitle>
        </ModalDialogHeader>
        <div className="flex justify-center">{Array.from({ length: 5 }, (_, i) => renderStar(i + 1))}</div>
        <div className="flex justify-center">
          <Button
            variant="ghost"
            className={"flex gap-1"}
            onClick={() => {
              console.log("Clicked");
              if (selectedStars < 0) {
                setSelectedStars(0);
              } else {
                setSelectedStars(-1);
              }
            }}
          >
            {selectedStars < 0 ? (
              <ThumbDownFillLogo className="h-6 w-6 fill-red-500" />
            ) : (
              <ThumbDownLineLogo className="h-6 w-6 fill-primary-100" />
            )}
            <p className={cn(selectedStars < 0 ? "text-red-500" : "text-primary-100")}>Schlechte Antwort</p>
          </Button>
          <div className="flex items-center pl-1 font-bold"></div>
        </div>
        <div className="flex justify-end">
          <RatingDialogPopup />
        </div>
        <Textarea
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          placeholder={"Kommentar (optional)"}
          className="flex h-full w-full border-none bg-primary-100 bg-opacity-10 p-3 pr-32 focus:outline-none"
        />
        <ModalDialogFooter>
          <ModalDialogCancel>Abbrechen</ModalDialogCancel>
          <ModalDialogAction
            onClick={() => {
              console.log("Hier wird bald das Rating an die KI verschickt...");
              const newRating: RateKiBotProps = {
                productId: productId,
                comment: comment,
                conversationId: conversationId,
                messageId: answerId,
                rating: selectedStars,
              };
              mutation.mutate(newRating);
            }}
          >
            Abschicken
          </ModalDialogAction>
        </ModalDialogFooter>
      </ModalDialogContent>
    </ModalDialog>
  );
};

export default RatingDialog;
