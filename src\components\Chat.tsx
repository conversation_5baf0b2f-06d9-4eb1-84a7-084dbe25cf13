import React, { useRef, useEffect } from "react";
import ChatMessage, { ChatMessageProps } from "./ChatMessage.tsx";
import LoadingSkeleton from "./LoadingSkeleton.tsx";
import { Typography } from "dbh-reactui";

const Chat: React.FC<{ messages: ChatMessageProps[]; isPending: boolean }> = ({ messages, isPending }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [messages]);

  return (
    <div
      ref={containerRef}
      className="flex h-full flex-col items-center gap-5 overflow-y-auto rounded bg-gray-100 p-3"
    >
      <div className="flex rounded-md bg-warning p-1">
        <Typography variant="caption_2">
          Die Nachrichten werden von einer KI beantwortet und können fehlerhaft sein.
        </Typography>
      </div>
      {messages.map((message, index) => (
        <ChatMessage key={index} {...message} />
      ))}
      {isPending && <LoadingSkeleton />}
    </div>
  );
};

export default Chat;
