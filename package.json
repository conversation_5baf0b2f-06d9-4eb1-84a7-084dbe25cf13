{"name": "chatbot-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "production": "tsc && vite build --mode production"}, "dependencies": {"@tanstack/react-query": "^5.63.0", "@tanstack/react-query-devtools": "^5.63.0", "clsx": "^2.1.1", "dbh-reactui": "^1.3.32", "dbh-reactui-tokens": "^1.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.6.0", "vite-plugin-svgr": "^4.3.0"}, "devDependencies": {"@eslint/js": "^9.17.0", "@tanstack/eslint-plugin-query": "^5.62.16", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.4.49", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}