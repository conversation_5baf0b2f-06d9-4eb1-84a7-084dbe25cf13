import React, { ReactNode, useState } from "react";
import ChatMessagesContext, { SavedChatMessageProps } from "../context/chat-messages-context.ts";

export const ChatMessagesProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [savedChatMessages, setSavedChatMessages] = useState<Record<string, SavedChatMessageProps[]>>({});

  return (
    <ChatMessagesContext.Provider value={{ savedChatMessages, setSavedChatMessages }}>
      {children}
    </ChatMessagesContext.Provider>
  );
};

export default ChatMessagesProvider;
