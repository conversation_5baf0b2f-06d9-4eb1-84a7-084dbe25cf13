import React from "react";
import { cn } from "../Utils.ts";
import RatingDialog from "./RatingDialog.tsx";
import { Typography } from "dbh-reactui";

export type ChatMessageProps =
  | {
      productId: string;
      type: "question" | "error";
      children: string;
      conversationId?: string;
      answerId?: never;
      timestamp: string;
    }
  | {
      productId: string;
      type: "answer";
      children: string;
      conversationId: string;
      answerId: string;
      timestamp: string;
    };

const ChatMessage: React.FC<ChatMessageProps> = ({
  productId,
  children,
  type,
  conversationId,
  answerId,
  timestamp,
}) => {
  const isQuestion: boolean = type === "question";
  const isError: boolean = type === "error";

  const Triangle = () => {
    return (
      <div
        className={cn(
          "h-0 w-0 border-t-[15px] border-l-transparent border-r-transparent",
          isQuestion
            ? "border-l-[15px] border-t-white"
            : cn(
                "border-r-[15px]",
                isError ? "border-t-error border-opacity-30" : "border-t-primary-100 border-opacity-70",
              ),
        )}
      ></div>
    );
  };

  return (
    <div className={cn("flex w-full", isQuestion && "justify-end")}>
      <div className="flex max-w-[675px] flex-col">
        <div
          className={cn(
            "flex rounded-t-xl p-2",
            isQuestion
              ? "bg-white"
              : cn(isError ? "bg-error bg-opacity-30" : "rounded-r-xl bg-primary-100 bg-opacity-70"),
          )}
        >
          <Typography variant="body_1">{children}</Typography>
        </div>
        <div className={cn("flex w-full", isQuestion && "text-end")}>
          {isQuestion ? null : <Triangle />}
          <Typography variant="caption_2" className="w-full text-gray-300">
            {timestamp}
          </Typography>
          {type === "answer" ? (
            <RatingDialog productId={productId} conversationId={conversationId} answerId={answerId} />
          ) : null}
          {isQuestion ? <Triangle /> : null}
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;
