import { SavedChatMessageProps } from "../context/chat-messages-context.ts";
import { ChatMessageProps } from "../components/ChatMessage.tsx";

export class ChatMessageUtil {
  /**
   * Wandelt SavedChatMessageProps[] in ChatMessageProps[] um.
   */
  static savedMessagesToChatMessages(savedMessages: SavedChatMessageProps[]): ChatMessageProps[] {
    if (!savedMessages || savedMessages.length === 0) {
      return [];
    }
    return savedMessages.map((s) => {
      if (s.type === "answer") {
        if (!s.conversationId || s.answerId === undefined) {
          throw new Error(
            "SavedChatMessageProps vom Typ 'answer' muss conversationId und answerId enthalten.",
          );
        }
        return {
          productId: s.productId,
          type: "answer",
          children: s.children,
          conversationId: s.conversationId,
          answerId: s.answerId,
          timestamp: s.timestamp,
        };
      } else {
        // Fragetypen und Fehlernachrichten
        const msg: {
          productId: string;
          type: "question" | "error";
          children: string;
          timestamp: string;
          conversationId?: string;
        } = {
          productId: s.productId,
          type: s.type,
          children: s.children,
          timestamp: s.timestamp,
        };
        if (s.conversationId) {
          console.log("IS CONVERSATION ID");
          msg.conversationId = s.conversationId;
        }
        return msg;
      }
    });
  }

  /**
   * Wandelt ChatMessageProps[] in SavedChatMessageProps[] um.
   */
  static chatMessagesToSavedMessages(chatMessages: ChatMessageProps[]): SavedChatMessageProps[] {
    return chatMessages.map((c) => {
      const base: Omit<SavedChatMessageProps, "answerId"> & {
        answerId?: string;
      } = {
        productId: c.productId,
        type: c.type,
        children: c.children,
        timestamp: c.timestamp,
        conversationId: c.conversationId,
      };
      if (c.type === "answer") {
        return {
          ...base,
          answerId: c.answerId,
        };
      } else {
        return {
          ...base,
        } as SavedChatMessageProps;
      }
    });
  }
}
