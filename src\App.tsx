import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>po<PERSON>,
} from "dbh-reactui";
import { useEffect } from "react";
import InfoPopup from "./components/InfoPopup.tsx";
import ChatWindow from "./components/ChatWindow.tsx";

function App() {
  useEffect(() => {
    document.title = import.meta.env.VITE_NAME;
  }, []);

  const getStage = () => {
    const hostname = window.location.hostname;
    const hostnameElements = hostname.split(".");
    if (hostnameElements.length > 0) {
      const serviceHostname = hostnameElements[0];
      const serviceHostnameElements = serviceHostname.split("-");
      if (serviceHostnameElements.length <= 1) {
        if (serviceHostnameElements[0] === "localhost") {
          return "LOCAL";
        }
      }
      return serviceHostnameElements[serviceHostnameElements.length - 1].toUpperCase();
    }
    return "";
  };

  const getBotNames = (): string[] => {
    const raw = import.meta.env.VITE_BOT_NAMES ?? "";
    return raw
      .split(",")
      .map((s: string) => s.trim())
      .filter(Boolean);
  };

  const renderTabs = () => {
    const botNames = getBotNames();
    return botNames.map((name, index) => {
      return (
        <Tab value={name.toLowerCase()} key={index}>
          {name.toUpperCase()}
        </Tab>
      );
    });
  };

  const renderTabsContent = () => {
    const botNames = getBotNames();
    return botNames.map((name, index) => {
      const productId = name.toLowerCase();
      return (
        <TabsContent
          key={index}
          value={productId}
          className={
            "data-[state=active]:flex data-[state=active]:h-[610px] data-[state=active]:max-h-[610px] data-[state=active]:w-full data-[state=active]:flex-col"
          }
        >
          <ChatWindow productId={productId} />
        </TabsContent>
      );
    });
  };

  const renderStage = () => {
    return (
      <Typography variant="caption_1_strong" className="flex rounded-md bg-warning p-1">
        {getStage()}
      </Typography>
    );
  };

  return (
    <div className="flex h-screen w-full items-center justify-center">
      <Card className="flex h-[700px] w-[900px]">
        <CardHeader className="flex w-full items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle>{import.meta.env.VITE_NAME}</CardTitle>
            {renderStage()}
          </div>
          <InfoPopup />
        </CardHeader>
        <CardContent className="flex h-full w-full flex-col">
          <Tabs orientation="vertical" defaultValue="alpo" className="flex gap-4">
            <TabsList orientation={"vertical-left"} tabProps={{ labelPosition: "left" }} className="w-32">
              <Typography variant={"subtitle_1"} className={"mb-6 mt-4"}>
                Bots
              </Typography>
              {renderTabs()}
            </TabsList>
            {renderTabsContent()}
          </Tabs>
          <Toaster />
        </CardContent>
      </Card>
    </div>
  );
}

export default App;
