import React from "react";
import { Pop<PERSON>, <PERSON>overContent, PopoverTrigger, Typography } from "dbh-reactui";
import InfoLogo from "../assets/information-line.svg?react";
import { KiBotInfoOnSuccessParams, KiBotService } from "../KiBotService.ts";
import { useQuery } from "@tanstack/react-query";

const ForwardInfoLogo = React.forwardRef<HTMLDivElement, React.SVGProps<SVGSVGElement>>((props, ref) => (
  <div ref={ref}>
    <InfoLogo {...props} />
  </div>
));

const InfoPopup: React.FC = () => {
  const { data } = useQuery<KiBotInfoOnSuccessParams>({
    queryKey: ["kiBotInfoQuery"],
    queryFn: KiBotService.getKiBotInfo,
    initialData: { build_timestamp: "-", version: "-", service: "-" },
    refetchInterval: 30000,
  });

  return (
    <Popover>
      <PopoverTrigger asChild>
        <ForwardInfoLogo className="h-5 w-5 cursor-pointer" />
      </PopoverTrigger>
      <PopoverContent className="flex w-full flex-col gap-1 focus-visible:outline-none">
        <div className="flex gap-2">
          <Typography variant={"caption_1_stronger"}>Chatbot-Version:</Typography>
          <Typography variant={"caption_1"}>{import.meta.env.VITE_VERSION}</Typography>
        </div>
        <div className="flex gap-2">
          <Typography variant={"caption_1_stronger"}>Chatbot-Build-Datum:</Typography>
          <Typography variant={"caption_1"}>{import.meta.env.VITE_VERSION_BUILD_TIMESTAMP}</Typography>
        </div>
        <div className="flex gap-2">
          <Typography variant={"caption_1_stronger"}>Backend-Version:</Typography>
          <Typography variant={"caption_1"}>{data.version}</Typography>
        </div>
        <div className="flex gap-2">
          <Typography variant={"caption_1_stronger"}>Backend-Build-Datum:</Typography>
          <Typography variant={"caption_1"}>{data.build_timestamp}</Typography>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default InfoPopup;
