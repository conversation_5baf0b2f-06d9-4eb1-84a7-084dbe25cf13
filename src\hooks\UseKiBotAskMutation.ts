import { useMutation, UseMutationResult } from "@tanstack/react-query";
import { useState } from "react";
import { AskKiBotOnSuccessParams, AskKiBotProps, KiBotService, OnErrorParams } from "../KiBotService";
import { getCurrentFormattedDate } from "../DateUtils.ts";
import { ChatMessageProps } from "../components/ChatMessage.tsx";
import { useChatMessageContext } from "../context/chat-messages-context.ts";
import { ChatMessageUtil } from "../utils/SavedChatMessageUtil.ts";

type AskProps = { question: string; productId: string };

function useKiBotAskMutation(productId: string) {
  const { savedChatMessages } = useChatMessageContext();
  const [conversationId, setConversationId] = useState<string>("");
  const [messages, setMessages] = useState<ChatMessageProps[]>(
    ChatMessageUtil.savedMessagesToChatMessages(savedChatMessages[productId]),
  );

  async function askQuestion({ question, productId }: AskProps): Promise<AskKiBotOnSuccessParams> {
    const newMessage: ChatMessageProps = {
      productId: productId,
      type: "question",
      timestamp: getCurrentFormattedDate(),
      children: question,
    };
    setMessages((prevMessages) => [...prevMessages, newMessage]);
    const askProps: AskKiBotProps = {
      conversationId: conversationId,
      question: question,
      productId: productId,
    };
    return await KiBotService.askKiBot(askProps);
  }

  function handleSuccess(answer: AskKiBotOnSuccessParams) {
    const newAnswer: ChatMessageProps = {
      productId: productId,
      type: "answer",
      conversationId: answer.conversationId,
      answerId: answer.messageId,
      timestamp: getCurrentFormattedDate(),
      children: answer.answer,
    };
    console.log(newAnswer);
    setMessages((prevMessages) => [...prevMessages, newAnswer]);
    setConversationId(answer.conversationId);
  }

  function handleError(error: OnErrorParams) {
    console.error(error.message);
    const newError: ChatMessageProps = {
      productId: productId,
      type: "error",
      timestamp: getCurrentFormattedDate(),
      children: error.message,
    };
    setMessages((prevMessages) => [...prevMessages, newError]);
  }

  const mutation: UseMutationResult<AskKiBotOnSuccessParams, OnErrorParams, AskProps> = useMutation({
    mutationFn: askQuestion,
    onSuccess: handleSuccess,
    onError: handleError,
  });

  return { mutation, messages };
}

export default useKiBotAskMutation;
