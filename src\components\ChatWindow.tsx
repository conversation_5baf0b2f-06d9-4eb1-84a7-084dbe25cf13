import React, { useEffect, useState } from "react";
import Chat from "./Chat.tsx";
import ChatInput from "./ChatInput.tsx";
import useKiBotAskMutation from "../hooks/UseKiBotAskMutation.ts";
import { useChatMessageContext } from "../context/chat-messages-context.ts";
import { ChatMessageUtil } from "../utils/SavedChatMessageUtil.ts";

type ChatWindowProps = {
  productId: string;
};

const ChatWindow: React.FC<ChatWindowProps> = ({ productId }) => {
  const [question, setQuestion] = useState<string>("");
  const { mutation, messages } = useKiBotAskMutation(productId);

  const { savedChatMessages, setSavedChatMessages } = useChatMessageContext();
  const savedProductMessages = ChatMessageUtil.savedMessagesToChatMessages(savedChatMessages[productId]);

  useEffect(() => {
    setSavedChatMessages((prev) => ({
      ...prev,
      [productId]: ChatMessageUtil.chatMessagesToSavedMessages(messages),
    }));
    console.log(savedChatMessages);
  }, [messages]);

  const handleQuestionInputClick = () => {
    if (!question) return;
    mutation.mutate({ question, productId });
    setQuestion("");
  };

  // Funktion zum Verarbeiten des Kommentars
  const handleCommentSubmit = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault(); // Standardaktion verhindern
      if (question.trim()) {
        mutation.mutate({ question, productId });
        setQuestion("");
      }
    }
  };

  return (
    <div className="flex h-full w-full flex-col gap-3">
      <Chat messages={savedProductMessages} isPending={mutation.isPending} />
      <ChatInput
        value={question}
        isPending={mutation.isPending}
        onValueChange={setQuestion}
        onSubmit={handleCommentSubmit}
        onClick={handleQuestionInputClick}
      />
    </div>
  );
};

export default ChatWindow;
