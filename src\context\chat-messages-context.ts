import React, { createContext, useContext } from "react";

export type SavedChatMessageProps = {
  productId: string;
  type: "question" | "error" | "answer";
  children: string;
  conversationId?: string;
  answerId?: string;
  timestamp: string;
  rated?: number;
  comment?: string;
};

// Definiert den Context-Typ
interface ChatMessagesContextType {
  savedChatMessages: Record<string, SavedChatMessageProps[]>;
  setSavedChatMessages: React.Dispatch<React.SetStateAction<Record<string, SavedChatMessageProps[]>>>;
}

// Erstelle Context
const ChatMessagesContext = createContext<ChatMessagesContextType | null>(null);

// Custom Hook für einfachen Zugriff
export const useChatMessageContext = () => {
  const context = useContext(ChatMessagesContext);
  if (!context) throw new Error("useChatMessageContext must be used within ChatProvider");
  return context;
};

// Exportiere Context selbst für Provider
export default ChatMessagesContext;
